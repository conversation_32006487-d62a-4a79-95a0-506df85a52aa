from collections import Counter

#
import spacy

#
import requests

nlp = spacy.load("fr_core_news_md")


def wikidata_search(query, language="fr", limit=5):
    url = "https://www.wikidata.org/w/api.php"
    params = {
        "action": "wbsearchentities",
        "format": "json",
        "language": language,
        "search": query,
        "limit": limit,
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    r = requests.get(url, params=params, headers=headers, timeout=10)
    results = r.json().get("search", [])
    return results


def wikidata_entity_info(entity_id):
    url = f"https://www.wikidata.org/wiki/Special:EntityData/{entity_id}.json"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    r = requests.get(url, headers=headers, timeout=10)
    data = r.json()
    entity = data["entities"][entity_id]

    sitelinks_count = len(entity.get("sitelinks", {}))
    label = (
        entity.get("labels", {})
        .get("fr", {})
        .get("value", entity.get("labels", {}).get("en", {}).get("value", ""))
    )
    description = (
        entity.get("descriptions", {})
        .get("fr", {})
        .get("value", entity.get("descriptions", {}).get("en", {}).get("value", ""))
    )

    return {
        "id": entity_id,
        "label": label,
        "description": description,
        "sitelinks": sitelinks_count,
    }


def extract_key_entities(text, top_n=5):
    doc = nlp(text)

    ents = [ent.text for ent in doc.ents]  # if ent.label_ in {"PER", "LOC", "ORG"}]

    results = []

    for ent_text in ents:
        candidates = wikidata_search(ent_text)

        if not candidates:
            continue

        entity_id = candidates[0]["id"]
        info = wikidata_entity_info(entity_id)

        results.append(
            {
                "text": ent_text,
                "id": entity_id,
                "label": info["label"],
                "description": info["description"],
                "sitelinks": info["sitelinks"],
            }
        )

    results_sorted = sorted(results, key=lambda x: x["sitelinks"], reverse=True)

    return results_sorted[:top_n]


texte = """
Emmanuel Macron s'est rendu à Bruxelles pour rencontrer Ursula von der Leyen.
Macron a discuté de l'Union européenne et de la Commission européenne.
"""

key_entities = extract_key_entities(texte, top_n=5)

for ent in key_entities:
    print(f"{ent['label']} ({ent['id']})")
    print(f" - Description: {ent['description']}")
    print(f" - Popularité (sitelinks): {ent['sitelinks']}")
    print()

#!/bin/bash

# Script d'installation des dépendances Python
# Utilise le fichier requirements.txt pour installer les packages

set -e  # Arrêt du script en cas d'erreur

clear

echo "=== Installation des dépendances Python ==="

if [ ! -f "requirements.txt" ]; then
    echo "❌ Erreur: Le fichier requirements.txt n'existe pas dans le répertoire courant"
    exit 1
fi

if ! command -v python3 &> /dev/null; then
    echo "❌ Erreur: Python3 n'est pas installé"
    exit 1
fi

if [ ! -d ".venv" ]; then
    echo "🐍  Création de l'environnement virtuel (.venv)…"
    python3 -m venv .venv
fi

echo "🔐 Activation de l'environnement virtuel…"
# shellcheck disable=SC1091
source .venv/bin/activate

echo "🔄 Mise à jour de pip..."
pip install --upgrade pip

echo "🔄 Installation des packages depuis requirements.txt..."
pip install -r requirements.txt

echo "✅ Installation terminée avec succès!"
echo "   • Python .venv: $(python --version), pip $(pip --version)"
echo "   • Node.js: node $(node --version), npm $(npm --version), npx $(npx --version)"
echo ""

#echo "📋 Packages installés:"
#python3 -m pip list --format=columns
#echo ""
#echo "🎉 Installation complète!"
